import api from '../../services/api'
import {Tenant} from '../../pages/tenants/types'

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: 'name' | 'updatedAt' | 'createdAt'
    sortingOrder: 'ASC' | 'DESC' | null
}

export const getTenants = async (options: Partial<Sorting> & Pagination) => {
    const response = await fetch(api.allTenants(options)).then((r) => r.json())
    return {
        data: response.data || [],
        pagination: response.meta || {page: 1, perPage: 10, total: 0},
    }
}

export const addTenant = async (tenant: Omit<Tenant, 'id' | 'createdAt' | 'updatedAt'>) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')

    console.log("tenant:...........", tenant)
    const {id, code, createdAt, updatedAt, ...rawPayload} = tenant

    const payload = Object.fromEntries(
        Object.entries(rawPayload).filter(
            ([, value]) => value !== undefined && value !== null && value !== ''
        )
    )
    console.log("payload:...........", payload)

    const response = await fetch(api.allTenants(), {
        method: 'POST',
        body: JSON.stringify(tenant),
        headers
    }).then((r) => r.json())
    console.log("response:...........", response)
    return response.data
}

export const updateTenant = async (tenant: Tenant) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')
    console.log("tenant:...........", tenant)
    const {id, code, createdAt, updatedAt, ...rawPayload} = tenant

    const payload = Object.fromEntries(
        Object.entries(rawPayload).filter(
            ([, value]) => value !== undefined && value !== null && value !== ''
        )
    )
    console.log("payload:...........", payload)

    const response = await fetch(api.tenant(id), {
        method: 'PATCH',
        body: JSON.stringify(payload),
        headers,
    }).then((r) => r.json())

    console.log("response:...........", response)

    return response.data
}


export const removeTenant = async (tenant: Tenant) => {
    const response = await fetch(api.tenant(tenant.id), {method: 'DELETE'})
    return response.ok
}
